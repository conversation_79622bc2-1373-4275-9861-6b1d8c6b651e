package com.smxz.service;

import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;

@Service
public class DocumentConversionService {

    /**
     * 将Word文档转换为PDF
     * @param file Word文档文件
     * @return PDF字节数组
     * @throws Exception 转换异常
     */
    public byte[] convertWordToPdf(MultipartFile file) throws Exception {
        try (InputStream inputStream = file.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            // 加载Word文档
            Document document = new Document(inputStream);

            // 保存为PDF格式
            document.save(outputStream, SaveFormat.PDF);

            return outputStream.toByteArray();
        }
    }

    /**
     * 验证文件是否为支持的Word格式
     * @param filename 文件名
     * @return 是否支持
     */
    public boolean isSupportedWordFormat(String filename) {
        if (filename == null) {
            return false;
        }
        String lowerCaseFilename = filename.toLowerCase();
        return lowerCaseFilename.endsWith(".doc") || lowerCaseFilename.endsWith(".docx");
    }
}
