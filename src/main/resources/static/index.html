<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word转PDF服务</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        input[type="file"] {
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Word转PDF转换服务</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>拖拽Word文件到此处，或点击选择文件</p>
            <input type="file" id="fileInput" accept=".doc,.docx" style="display: none;">
            <button type="button" onclick="document.getElementById('fileInput').click()">选择Word文件</button>
        </div>
        
        <button id="convertBtn" onclick="convertFile()" disabled>转换为PDF</button>
        
        <div id="message"></div>
    </div>

    <script>
        let selectedFile = null;
        
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const convertBtn = document.getElementById('convertBtn');
        const messageDiv = document.getElementById('message');
        
        // 文件选择事件
        fileInput.addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });
        
        // 拖拽事件
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFileSelect(e.dataTransfer.files[0]);
        });
        
        function handleFileSelect(file) {
            if (!file) return;
            
            if (!file.name.toLowerCase().endsWith('.doc') && !file.name.toLowerCase().endsWith('.docx')) {
                showMessage('请选择.doc或.docx格式的Word文件', 'error');
                return;
            }
            
            selectedFile = file;
            uploadArea.innerHTML = `<p>已选择文件: ${file.name}</p><p>文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>`;
            convertBtn.disabled = false;
            showMessage('', '');
        }
        
        function convertFile() {
            if (!selectedFile) {
                showMessage('请先选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', selectedFile);
            
            convertBtn.disabled = true;
            showMessage('正在转换中，请稍候...', 'loading');
            
            fetch('/api/document/word-to-pdf', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    return response.json().then(err => Promise.reject(err));
                }
            })
            .then(blob => {
                // 下载PDF文件
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = selectedFile.name.replace(/\.(doc|docx)$/i, '.pdf');
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                showMessage('转换成功！PDF文件已开始下载', 'success');
            })
            .catch(error => {
                console.error('转换失败:', error);
                showMessage('转换失败: ' + (error.error || '未知错误'), 'error');
            })
            .finally(() => {
                convertBtn.disabled = false;
            });
        }
        
        function showMessage(text, type) {
            messageDiv.innerHTML = text;
            messageDiv.className = 'message ' + type;
            if (!text) {
                messageDiv.style.display = 'none';
            } else {
                messageDiv.style.display = 'block';
            }
        }
    </script>
</body>
</html>
