package com.smxz;

import com.smxz.service.DocumentConversionService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class DocumentConversionServiceTest {

    @Autowired
    private DocumentConversionService documentConversionService;

    @Test
    public void testIsSupportedWordFormat() {
        assertTrue(documentConversionService.isSupportedWordFormat("test.doc"));
        assertTrue(documentConversionService.isSupportedWordFormat("test.docx"));
        assertTrue(documentConversionService.isSupportedWordFormat("TEST.DOC"));
        assertTrue(documentConversionService.isSupportedWordFormat("TEST.DOCX"));
        
        assertFalse(documentConversionService.isSupportedWordFormat("test.pdf"));
        assertFalse(documentConversionService.isSupportedWordFormat("test.txt"));
        assertFalse(documentConversionService.isSupportedWordFormat(null));
        assertFalse(documentConversionService.isSupportedWordFormat(""));
    }

    @Test
    public void testConvertWordToPdfWithEmptyFile() {
        MockMultipartFile emptyFile = new MockMultipartFile(
            "file", 
            "empty.docx", 
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document", 
            new byte[0]
        );

        assertThrows(Exception.class, () -> {
            documentConversionService.convertWordToPdf(emptyFile);
        });
    }
}
